import React, { useEffect, useRef, useState } from 'react';
import ChatMessage from '../components/Chat/ChatMessage';
import ChatInput from '../components/Chat/ChatInput';
import ChatHistorySidebar from '../components/Chat/ChatHistorySidebar';
import { useChat } from '../contexts/ChatContext';

const Chatbot = () => {
  const {
    messages,
    currentVannaId,
    isProcessing,
    isSwitchingChat,
    currentEventSourceRef,
    setMessages,
    setCurrentVannaId,
    setIsProcessing,
    addMessage,
    updateLastMessage,
    updateMessage,
    clearChatHistory
  } = useChat();

  const chatHistoryRef = useRef(null);
  const [isChatHistoryOpen, setIsChatHistoryOpen] = useState(() => {
    const saved = localStorage.getItem('chatHistoryOpen');
    return saved ? JSON.parse(saved) : false;
  });

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (chatHistoryRef.current) {
      chatHistoryRef.current.scrollTop = chatHistoryRef.current.scrollHeight;
    }
  }, [messages]);

  // Save sidebar state to localStorage
  useEffect(() => {
    localStorage.setItem('chatHistoryOpen', JSON.stringify(isChatHistoryOpen));
  }, [isChatHistoryOpen]);



  const sendMessage = async (question) => {
    if (!question.trim() || isProcessing) return;

    setIsProcessing(true);

    // Generate a unique query session ID for this specific query
    const querySessionId = `query_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Add user message
    addMessage(question, 'user');

    // Close any existing EventSource connection
    if (currentEventSourceRef.current) {
      currentEventSourceRef.current.close();
    }

    // Create initial assistant message
    addMessage('', 'bot', {
      isLoading: true,
      isThinking: false,
      querySessionId: querySessionId // Add query session ID to track this specific query
    });

    // Build API URL - use direct backend URL for EventSource
    let apiUrl = `http://localhost:8000/api/v1/sql/generate_sql?question=${encodeURIComponent(question)}`;
    if (currentVannaId) {
      apiUrl += `&existing_id=${currentVannaId}`;
    }

    try {
      // Start streaming response
      const eventSource = new EventSource(apiUrl);
      currentEventSourceRef.current = eventSource;
      
      let accumulatedResponse = '';
      let firstChunkProcessed = false;
      let isCurrentlyThinking = false;

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

        if (data.type === 'thinking_on') {
          updateLastMessage({
            content: '',
            isThinking: true,
            isLoading: false
          });
          isCurrentlyThinking = true;
          firstChunkProcessed = true;

        } else if (data.type === 'thinking_off') {
          if (isCurrentlyThinking) {
            updateLastMessage({
              content: '',
              isThinking: false,
              isLoading: false
            });
          }
          isCurrentlyThinking = false;
          
        } else if (data.type === 'chunk') {
          if (isCurrentlyThinking || !firstChunkProcessed) {
            updateLastMessage({
              content: '',
              isThinking: false,
              isLoading: false
            });
            isCurrentlyThinking = false;
          }

          if (!firstChunkProcessed) {
            firstChunkProcessed = true;
          }

          accumulatedResponse += data.content;
          updateLastMessage({
            content: accumulatedResponse,
            isLoading: false,
            isThinking: false,
            isStreaming: true,
            querySessionId: querySessionId // Tag chunk messages with query session ID
          });
          
        } else if (data.type === 'intermediate_notification') {
          // Hide the first message that contains both intermediate and final SQL
          updateLastMessage({
            isHidden: true
          });

          // Start with a simple notification message
          addMessage('', 'bot', {
            content: data.message,
            isLoading: false,
            isThinking: false,
            isStreaming: false,
            querySessionId: querySessionId
          });

        } else if (data.type === 'intermediate_sql') {
          // Create or update intermediate message for this iteration
          const iteration = data.iteration || 1;

          // Check if we already have an intermediate message for this iteration AND query session
          const existingMessageIndex = messages.findIndex(msg =>
            msg.isIntermediateStep &&
            msg.iteration === iteration &&
            msg.querySessionId === querySessionId
          );

          if (existingMessageIndex !== -1) {
            // Update existing intermediate message
            setMessages(prevMessages => {
              const newMessages = [...prevMessages];
              newMessages[existingMessageIndex] = {
                ...newMessages[existingMessageIndex],
                intermediateSql: data.sql,
                intermediateSqlMessage: data.message,
                hasResults: false
              };
              return newMessages;
            });
          } else {
            // Create new intermediate message
            addMessage('', 'bot', {
              isIntermediateStep: true,
              iteration: iteration,
              querySessionId: querySessionId, // Add query session ID
              intermediateSql: data.sql,
              intermediateSqlMessage: data.message,
              hasResults: false,
              isLoading: false,
              isThinking: false,
              isStreaming: false
            });
          }

        } else if (data.type === 'intermediate_results') {
          // Update the intermediate message with results
          const iteration = data.iteration || 1;

          setMessages(prevMessages => {
            const newMessages = [...prevMessages];
            const messageIndex = newMessages.findIndex(msg =>
              msg.isIntermediateStep &&
              msg.iteration === iteration &&
              msg.querySessionId === querySessionId
            );

            if (messageIndex !== -1) {
              newMessages[messageIndex] = {
                ...newMessages[messageIndex],
                intermediateResults: data.results,
                totalRows: data.total_rows,
                intermediateResultsMessage: data.message,
                hasResults: true
              };
            }
            return newMessages;
          });

          // Hide any raw intermediate_sql messages after results are shown
          setTimeout(() => {
            const currentQuerySessionId = querySessionId; // Capture in closure
            setMessages(prevMessages => {
              const newMessages = [...prevMessages];
              // Find and hide raw intermediate SQL messages that match the pattern
              // Only hide messages from the current query session
              for (let i = 0; i < newMessages.length; i++) {
                const msg = newMessages[i];
                if (msg.content &&
                    msg.content.trim().startsWith('intermediate_sql') &&
                    (msg.content.includes('SELECT') || msg.content.includes('INSERT') || msg.content.includes('UPDATE') || msg.content.includes('DELETE')) &&
                    !msg.isIntermediateStep && // Don't hide the structured intermediate step messages
                    msg.querySessionId === currentQuerySessionId && // Only hide messages from current query session
                    !msg.isHidden) {
                  newMessages[i] = { ...msg, isHidden: true };
                }
              }
              return newMessages;
            });
          }, 500); // Hide after 0.5 second delay

        } else if (data.type === 'next_iteration_start') {
          // Hide any previous thinking messages before starting new one
          setMessages(prevMessages => {
            const newMessages = [...prevMessages];
            for (let i = 0; i < newMessages.length; i++) {
              const msg = newMessages[i];
              if (msg.isThinking && !msg.isHidden) {
                newMessages[i] = { ...msg, isHidden: true };
              }
            }
            return newMessages;
          });

          // Start a new "thinking" message for the next iteration
          // Reset accumulated response for fresh streaming
          accumulatedResponse = "";

          addMessage('', 'bot', {
            isLoading: true,
            isThinking: true,
            isStreaming: false,
            thinkingMessage: data.message || `Generating next query based on ${data.iteration} intermediate result(s)...`,
            querySessionId: querySessionId
          });

        } else if (data.type === 'final_sql_generation_complete') {
          // Hide any thinking messages when final generation is complete
          setMessages(prevMessages => {
            const newMessages = [...prevMessages];
            for (let i = 0; i < newMessages.length; i++) {
              const msg = newMessages[i];
              if (msg.isThinking && !msg.isHidden) {
                newMessages[i] = { ...msg, isHidden: true };
              }
            }
            return newMessages;
          });

          // Reset accumulated response for fresh final SQL streaming
          accumulatedResponse = "";

          // Start a new message for the final SQL generation
          addMessage('', 'bot', {
            isLoading: true,
            isThinking: false,
            isStreaming: true,
            querySessionId: querySessionId
          });

        } else if (data.type === 'intermediate_step') {
          // Handle other intermediate processing steps
          updateLastMessage({
            content: `🔄 ${data.message}`,
            isLoading: true,
            isThinking: false,
            isStreaming: false
          });

        } else if (data.type === 'sql_result') {
          eventSource.close();
          currentEventSourceRef.current = null;
          setCurrentVannaId(data.id);

          // Check if this is the result of intermediate SQL processing
          if (data.used_intermediate_sql) {
            // Handle both single and multiple intermediate SQL cases
            const intermediateInfo = {};

            if (data.intermediate_queries && Array.isArray(data.intermediate_queries)) {
              // Multiple intermediate queries
              intermediateInfo.intermediateQueries = data.intermediate_queries;
              intermediateInfo.resultsCount = data.intermediate_results_count || [];
              intermediateInfo.intermediateCount = data.intermediate_count || data.intermediate_queries.length;
            } else if (data.intermediate_sql) {
              // Single intermediate query (backward compatibility)
              intermediateInfo.intermediateSql = data.intermediate_sql;
              intermediateInfo.resultsCount = data.intermediate_results_count;
            }

            // Show the full LLM response like before
            updateLastMessage({
              content: data.full_response || accumulatedResponse, // Use the full LLM response
              sqlQuery: data.text,
              vannaId: data.id,
              isLoading: false,
              isThinking: false,
              isStreaming: false,
              showSql: true,
              hideAssistantMessage: true,
              sqlTimestamp: new Date().toLocaleTimeString(),
              intermediateInfo: intermediateInfo
            });
          } else {
            // Regular SQL result
            updateLastMessage({
              content: accumulatedResponse, // Keep the full LLM response for toggle
              sqlQuery: data.text,
              vannaId: data.id,
              isLoading: false,
              isThinking: false,
              isStreaming: false,
              showSql: true,
              hideAssistantMessage: true,
              sqlTimestamp: new Date().toLocaleTimeString()
            });
          }

          // Hide any raw final SQL messages after results are shown
          setTimeout(() => {
            const currentQuerySessionId = querySessionId; // Capture in closure
            setMessages(prevMessages => {
              const newMessages = [...prevMessages];
              // Find and hide raw final SQL messages that match the pattern
              // Only hide messages from the current query session
              for (let i = 0; i < newMessages.length; i++) {
                const msg = newMessages[i];
                if (msg.content &&
                    (msg.content.trim().startsWith('final_sql') ||
                     (msg.content.includes('SELECT') || msg.content.includes('INSERT') ||
                      msg.content.includes('UPDATE') || msg.content.includes('DELETE'))) &&
                    !msg.isIntermediateStep && // Don't hide the structured intermediate step messages
                    !msg.showSql && // Don't hide the final SQL result message
                    msg.querySessionId === currentQuerySessionId && // Only hide messages from current query session
                    !msg.isHidden) {
                  newMessages[i] = { ...msg, isHidden: true };
                }
              }
              return newMessages;
            });
          }, 500); // Hide after 0.5 second delay

          // Execute the SQL and get results
          executeSqlQuery(data.id, data.text);

        } else if (data.type === 'chat_result') {
          eventSource.close();
          currentEventSourceRef.current = null;
          setCurrentVannaId(data.id);

          updateLastMessage({
            content: data.message,
            vannaId: data.id,
            isLoading: false,
            isThinking: false,
            isStreaming: false
          });

          setIsProcessing(false);
          
        } else if (data.type === 'error') {
          eventSource.close();
          currentEventSourceRef.current = null;
          
          updateLastMessage({
            content: `Error: ${data.error}`,
            isError: true,
            isLoading: false,
            isThinking: false
          });

          setIsProcessing(false);
        }
        } catch (error) {
          console.error('Error parsing EventSource data:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('EventSource error:', error);
        eventSource.close();
        currentEventSourceRef.current = null;
        
        updateLastMessage({
          content: 'Error: Connection lost. Please try again.',
          isError: true,
          isLoading: false,
          isThinking: false
        });
        
        setIsProcessing(false);
      };

    } catch (error) {
      console.error('Send message error:', error);
      updateLastMessage({
        content: 'Error: Failed to send message. Please try again.',
        isError: true,
        isLoading: false,
        isThinking: false
      });
      setIsProcessing(false);
    }
  };

  const executeSqlQuery = async (vannaId, sqlQuery) => {
    // Add execution status message
    const executionMessage = addMessage('', 'bot', {
      isLoading: true,
      executionStatus: 'Executing SQL query...'
    });

    // Set up a timer to update status if SQL execution takes too long (likely correction attempt)
    const correctionTimer = setTimeout(() => {
      updateMessage(executionMessage.id, {
        executionStatus: '⚠️ Original SQL failed - Attempting automatic correction...'
      });
    }, 3000); // After 3 seconds, assume correction is happening

    try {
      const response = await fetch(`/api/v1/sql/run_sql?id=${vannaId}`);

      // Check if response is ok or if it's an error response with JSON
      if (!response.ok && response.status !== 500) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Debug logging before parsing
      console.log('SQL execution response status:', response.status);
      console.log('SQL execution response headers:', Object.fromEntries(response.headers.entries()));

      // Check if response has content
      const responseText = await response.text();
      console.log('SQL execution response text:', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
        console.log('SQL execution parsed data:', data);
      } catch (parseError) {
        console.error('Failed to parse response as JSON:', parseError);
        console.error('Response text was:', responseText);
        throw new Error(`Invalid JSON response: ${responseText}`);
      }

      // Clear the correction timer since we got a response
      clearTimeout(correctionTimer);

      // Remove execution status message
      setMessages(prev => prev.filter(msg => msg.id !== executionMessage.id));

      if (data.type === "error") {
        // Update execution message to show SQL failed
        updateMessage(executionMessage.id, {
          isLoading: false,
          executionStatus: null
        });

        // Handle SQL execution errors with detailed feedback
        if (data.llm_explanation) {
          addMessage(`SQL Execution Failed: ${data.llm_explanation}`, 'bot', {
            isError: true,
            failedSql: data.failed_sql || sqlQuery,
            sqlTimestamp: new Date().toLocaleTimeString()
          });
        } else if (data.attempted_corrected_sql) {
          // Show that original SQL failed and correction was attempted but also failed
          addMessage(`❌ Original SQL failed - Attempted automatic correction also failed`, 'bot', {
            isError: true,
            failedSql: data.failed_sql,
            correctedSqlInfo: {
              originalSql: data.failed_sql,
              correctedSql: data.attempted_corrected_sql,
              bothFailed: true
            },
            sqlTimestamp: new Date().toLocaleTimeString(),
            content: `Error Details:\n- Initial SQL Error: ${data.error}\n- Correction attempt also failed`
          });
        } else if (data.llm_correction_output) {
          // Show that original SQL failed and correction was attempted but wasn't valid
          addMessage(`❌ Original SQL failed - Automatic correction was not valid`, 'bot', {
            isError: true,
            failedSql: data.failed_sql,
            sqlTimestamp: new Date().toLocaleTimeString(),
            content: `Error Details:\n- Initial SQL Error: ${data.error}\n- LLM provided invalid correction: ${data.llm_correction_output}`
          });
        } else {
          addMessage(`SQL Execution Failed: ${data.error}`, 'bot', {
            isError: true,
            failedSql: data.failed_sql || sqlQuery,
            sqlTimestamp: new Date().toLocaleTimeString(),
            content: `The SQL query could not be executed. Please try rephrasing your question.`
          });
        }
        setIsProcessing(false);
        return;
      }

      // SQL execution was successful
      const dfRecords = JSON.parse(data.df);

      if (data.corrected_sql_executed) {
        // Update the original message to show both failed and corrected SQL
        updateLastMessage({
          correctedSqlInfo: {
            originalSql: data.original_sql,
            correctedSql: data.corrected_sql_executed,
            bothFailed: false
          },
          sqlTimestamp: new Date().toLocaleTimeString()
        });
        addMessage("✅ Original SQL failed - Automatic correction was successful and executed.", 'bot', {
          sqlTimestamp: new Date().toLocaleTimeString()
        });
      }

      if (dfRecords.length > 0) {
        // Add table results
        addMessage('Data retrieved:', 'bot', {
          tableData: dfRecords,
          sqlQuery: sqlQuery
        });

        // Generate summary FIRST, then chart after summary completes
        generateSummaryThenChart(vannaId);
      } else {
        const noResultsMessage = data.message || "No results found for this query.";
        addMessage(noResultsMessage, 'bot');
        setIsProcessing(false);
      }

    } catch (error) {
      // Clear the correction timer
      clearTimeout(correctionTimer);

      console.error('SQL execution error:', error);

      // Update execution message to show error
      updateMessage(executionMessage.id, {
        isLoading: false,
        executionStatus: null
      });

      addMessage('An error occurred while running the SQL query or processing its results.', 'bot', {
        isError: true
      });
      setIsProcessing(false);
    }
  };

  const generateSummaryThenChart = (vannaId) => {
    // Generate summary first, then chart after summary completes
    generateSummary(vannaId, () => {
      // Callback to generate chart after summary is done
      generateChart(vannaId);
    });
  };

  const generateSummary = (vannaId, onComplete = null) => {
    // Create streaming summary message
    const summaryMessage = addMessage('', 'bot', {
      isLoading: true,
      summaryLoading: 'Generating summary...'
    });

    try {
      const eventSource = new EventSource(`http://localhost:8000/api/v1/analysis/generate_summary_stream?id=${vannaId}`);
      let summaryAccumulated = '';
      let summaryFirstChunkProcessed = false;
      let summaryIsThinking = false;

      eventSource.onmessage = function(event) {
        const data = JSON.parse(event.data);

        if (data.type === 'thinking_on') {
          setMessages(prev => prev.map(msg =>
            msg.id === summaryMessage.id
              ? { ...msg, isLoading: false, isThinking: true, content: '' }
              : msg
          ));
          summaryFirstChunkProcessed = true;
          summaryIsThinking = true;
        } else if (data.type === 'thinking_off') {
          if (summaryIsThinking) {
            setMessages(prev => prev.map(msg =>
              msg.id === summaryMessage.id
                ? { ...msg, isThinking: false, content: '' }
                : msg
            ));
          }
          summaryIsThinking = false;
        } else if (data.type === 'summary_chunk') {
          if (summaryIsThinking || !summaryFirstChunkProcessed) {
            summaryIsThinking = false;
            summaryFirstChunkProcessed = true;
          }
          summaryAccumulated += data.content;
          setMessages(prev => prev.map(msg =>
            msg.id === summaryMessage.id
              ? { ...msg, isLoading: false, isThinking: false, content: `Summary: ${summaryAccumulated}`, isStreaming: true }
              : msg
          ));
        } else if (data.type === 'summary_result') {
          eventSource.close();
          setMessages(prev => prev.map(msg =>
            msg.id === summaryMessage.id
              ? { ...msg, isLoading: false, isThinking: false, isStreaming: false, content: `Summary: ${data.summary}` }
              : msg
          ));
          // Summary is complete, now call the completion callback
          if (onComplete) {
            setTimeout(onComplete, 500); // Small delay like original HTML
          }
        } else if (data.type === 'error') {
          eventSource.close();
          setMessages(prev => prev.map(msg =>
            msg.id === summaryMessage.id
              ? { ...msg, isLoading: false, isThinking: false, content: 'Could not generate summary.', isError: true }
              : msg
          ));
          // Still try to generate chart even if summary fails
          if (onComplete) {
            console.log('Summary failed, but attempting chart generation anyway');
            setTimeout(onComplete, 500);
          }
        }
      };

      eventSource.onerror = function(error) {
        console.error('Summary EventSource failed:', error);
        eventSource.close();
        setMessages(prev => prev.map(msg =>
          msg.id === summaryMessage.id
            ? { ...msg, isLoading: false, isThinking: false, content: 'Failed to generate summary.', isError: true }
            : msg
        ));
        // Still try to generate chart even if summary EventSource fails
        if (onComplete) {
          console.log('Summary EventSource failed, but attempting chart generation anyway');
          setTimeout(onComplete, 500);
        }
      };

    } catch (error) {
      console.error('Summary generation error:', error);
      setMessages(prev => prev.map(msg =>
        msg.id === summaryMessage.id
          ? { ...msg, isLoading: false, isThinking: false, content: 'Error generating summary.', isError: true }
          : msg
      ));
      // Still try to generate chart even if summary generation throws an error
      if (onComplete) {
        console.log('Summary generation threw error, but attempting chart generation anyway');
        setTimeout(onComplete, 500);
      }
    }
  };

  const generateChart = async (vannaId) => {
    // Create chart loading message
    const chartMessage = addMessage('', 'bot', {
      isLoading: true,
      chartLoading: 'Generating chart...'
    });

    try {
      const response = await fetch(`/api/v1/analysis/generate_plotly_figure?id=${vannaId}`);
      const data = await response.json();

      // Remove loading message
      setMessages(prev => prev.filter(msg => msg.id !== chartMessage.id));

      if (data.type === "plotly_figure" && data.fig) {
        // Add chart results
        addMessage('', 'bot', {
          chartData: data.fig
        });
      } else if (data.type === "info") {
        addMessage(data.message, 'bot');
      } else if (data.type === "error") {
        console.warn("Plot error:", data.error);
        addMessage("Could not generate a chart for this data.", 'bot');
      } else if (data.type === "no_chart") {
        const message = data.message || "Chart not recommended for this type of data.";
        addMessage(message, 'bot');
      } else {
        addMessage("Chart could not be generated at this time.", 'bot');
      }
    } catch (error) {
      console.error('Chart generation error:', error);
      addMessage('Error fetching chart.', 'bot', { isError: true });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="flex h-full overflow-hidden relative">
      {/* Main Chat Area */}
      <div className={`flex flex-col p-8 transition-all duration-300 ease-in-out ${
        isChatHistoryOpen ? 'mr-80' : 'mr-0'
      } w-full`}>
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-4 mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            <i className="fas fa-robot mr-3"></i>
            Hospital Data Assistant
          </h2>

          {/* Header Actions */}
          <div className="flex items-center space-x-3">
            {/* Clear Current Chat */}
            <button
              onClick={clearChatHistory}
              className="flex items-center px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 border border-gray-300 dark:border-gray-600 rounded-lg hover:border-red-300 dark:hover:border-red-600 transition-colors"
              title="Clear current chat"
            >
              <i className="fas fa-eraser mr-2"></i>
              Clear Chat
            </button>

            {/* Toggle Chat History */}
            <button
              onClick={() => setIsChatHistoryOpen(!isChatHistoryOpen)}
              className={`flex items-center px-3 py-2 text-sm rounded-lg transition-colors ${
                isChatHistoryOpen
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 border border-blue-300 dark:border-blue-700'
                  : 'text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 border border-gray-300 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-600'
              }`}
              title="Toggle chat history"
            >
              <i className="fas fa-history mr-2"></i>
              History
            </button>
          </div>
        </div>

        {/* Chat Messages */}
        <div
          ref={chatHistoryRef}
          className={`flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900 rounded-lg p-4 mb-6 min-h-0 scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 transition-opacity duration-300 ${
            isSwitchingChat ? 'opacity-50' : 'opacity-100'
          }`}
        >
          {isSwitchingChat ? (
            <div className="flex items-center justify-center h-32">
              <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                <span className="text-sm">Loading chat...</span>
              </div>
            </div>
          ) : (
            <div className="chat-messages-container">
              {messages.map((message, index) => (
                <div
                  key={message.id}
                  className="chat-message-wrapper"
                  style={{
                    animationDelay: `${index * 50}ms`
                  }}
                >
                  <ChatMessage message={message} />
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Chat Input */}
        <ChatInput
          onSendMessage={sendMessage}
          disabled={isProcessing}
          placeholder="Type your question about hospital data..."
        />
      </div>

      {/* Chat History Sidebar - Smooth Animation */}
      <div
        className={`
          fixed top-0 right-0 h-screen w-80 z-50 bg-white dark:bg-gray-800
          border-l border-gray-200 dark:border-gray-700 shadow-lg
          transition-all duration-300 ease-in-out
          ${isChatHistoryOpen
            ? 'transform translate-x-0 opacity-100'
            : 'transform translate-x-full opacity-0 pointer-events-none'
          }
        `}
      >
        <ChatHistorySidebar
          isOpen={isChatHistoryOpen}
          onToggle={() => setIsChatHistoryOpen(!isChatHistoryOpen)}
        />
      </div>
    </div>
  );
};

export default Chatbot;
